<?php
/**
 * Steps Ahead Panel Component
 * Displays steps ahead content on listing pages after the bottom CTA
 * Content comes from Theme Settings, individual pages control styling and enable/disable
 */

// Check if Steps Ahead panels are globally enabled
$global_enable = get_field('steps_ahead_global_enable', 'option');
if (!$global_enable) {
    return;
}

$page_obj = get_queried_object();
$panel_settings = null;

// Check if we're on a supported page type and get the appropriate settings
if (is_tax('holiday-type')) {
    // Holiday Type taxonomy page
    $panel_settings = get_field('steps_ahead_panel', $page_obj);
} elseif (is_tax('holiday-regions')) {
    // Holiday Regions taxonomy page
    $panel_settings = get_field('steps_ahead_panel', $page_obj);
} elseif (is_post_type_archive('holiday')) {
    // All Holidays archive page - get settings from options page
    $panel_settings = get_field('steps_ahead_panel', 'option');
} else {
    // Not a supported page type
    return;
}

// Check if panel is enabled
if (!$panel_settings || !$panel_settings['enable_panel']) {
    return;
}

// Get global content from Theme Settings
$global_content = get_field('steps_ahead_global_content', 'option');
if (!$global_content) {
    return;
}

// Use global content for title and columns
$panel_title = $global_content['panel_title'] ?: '';
$panel_columns = $global_content['columns'];

// Use individual page settings for styling
$background_color = $panel_settings['background_color'] ?: '#ebf2f1'; // Default background color
$curved_edge = $panel_settings['curved_edge'] ?: 'bottom'; // Default to bottom curve
$flip_curved_edge = $panel_settings['flip_curved_edge'] ?: false;
$point_title_color = $panel_settings['point_title_color'] ?: '';
$column_background_color = $panel_settings['column_background_color'] ?: '';

if (!$panel_columns || empty($panel_columns)) {
    return;
}

// Set up background classes
$background_classes = 'has-background';
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
    if ($flip_curved_edge) {
        $background_classes .= ' curve-flipped';
    }
}

?>

<section class="steps-ahead steps-ahead--listing">
    <div class="steps-ahead__inner<?php echo $background_classes ? ' ' . $background_classes : ''; ?>" data-aos="fade" style="background-color: <?php echo $background_color; ?>;">
        <div class="container steps-ahead__container">
            <?php if($panel_title) : ?>
                <div class="steps-ahead__content centre inner-container">
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo esc_html($panel_title); ?></h2>
                </div>
            <?php endif; ?>

            <?php if($panel_columns) : ?>
                <div class="row steps-ahead__row">
                    <?php foreach($panel_columns as $column) : ?>
                        <?php
                        $ctitle = $column['title'] ?: '';
                        $cimage = $column['image'] ?: null;
                        $cpoints = $column['points'] ?: [];
                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre"<?php if($column_background_color) : ?> style="background-color: <?php echo $column_background_color; ?>;"<?php endif; ?>>
                                <?php if($cimage) : ?>
                                    <div class="steps-ahead__col-image">
                                        <img src="<?php echo $cimage['url']; ?>" alt="<?php echo $cimage['alt']; ?>">
                                    </div>
                                <?php endif; ?>

                                <?php if($ctitle) : ?>
                                    <h4 class="steps-ahead__col-heading"><?php echo esc_html($ctitle); ?></h4>
                                <?php endif; ?>

                                <?php if($cpoints && !empty($cpoints)) : ?>
                                    <div class="steps-ahead__col-points">
                                        <?php foreach($cpoints as $point) : ?>
                                            <?php
                                            $point_title = $point['title'] ?: '';
                                            $point_text = $point['text'] ?: '';
                                            ?>
                                            <?php if($point_title || $point_text) : ?>
                                                <div class="steps-ahead__point">
                                                    <?php if($point_title) : ?>
                                                        <h5 class="steps-ahead__point-title"<?php if($point_title_color) : ?> style="color: <?php echo $point_title_color; ?>;"<?php endif; ?>><?php echo esc_html($point_title); ?></h5>
                                                    <?php endif; ?>
                                                    <?php if($point_text) : ?>
                                                        <div class="steps-ahead__point-text">
                                                            <?php echo nl2br(esc_html($point_text)); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->
