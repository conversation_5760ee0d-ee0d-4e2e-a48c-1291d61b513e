<?php
/**
 * Steps Ahead Panel Component
 * Displays steps ahead content on listing pages after the bottom CTA
 * Content comes from Theme Settings for most pages, All Holidays archive has its own content
 */

$page_obj = get_queried_object();
$panel_settings = null;
$panel_title = '';
$panel_copy = '';
$panel_columns = [];

// Check if we're on a supported page type and get the appropriate settings
if (is_tax('holiday-type')) {
    // Holiday Type taxonomy page
    $panel_settings = get_field('steps_ahead_panel', $page_obj);

    // Get global content from Theme Settings
    $global_content = get_field('steps_ahead_global_content', 'option');
    if ($global_content && $global_content['panel_status']) {
        $panel_title = $global_content['panel_title'] ?: '';
        $panel_columns = $global_content['columns'] ?: [];
    }

} elseif (is_tax('holiday-regions')) {
    // Holiday Regions taxonomy page
    $panel_settings = get_field('steps_ahead_panel', $page_obj);

    // Get global content from Theme Settings
    $global_content = get_field('steps_ahead_global_content', 'option');
    if ($global_content && $global_content['panel_status']) {
        $panel_title = $global_content['panel_title'] ?: '';
        $panel_columns = $global_content['columns'] ?: [];
    }

} elseif (is_post_type_archive('holiday')) {
    // All Holidays archive page - get styling settings from archive page, content from Theme Settings
    $panel_settings = get_field('steps_ahead_panel', 'option');

    // Get global content from Theme Settings
    $global_content = get_field('steps_ahead_global_content', 'option');
    if ($global_content && $global_content['panel_status']) {
        $panel_title = $global_content['panel_title'] ?: '';
        $panel_columns = $global_content['columns'] ?: [];
    }

} else {
    // Not a supported page type
    return;
}

// Check if panel is enabled - all pages now use 'panel_status'
if (!$panel_settings || !$panel_settings['panel_status']) {
    return;
}

if (!$panel_columns || empty($panel_columns)) {
    return;
}

// Use individual page settings for styling - all pages now use the same field names
$background_color = $panel_settings['panel_background'] ?: '#ebf2f1';
$curved_edge = $panel_settings['curve_position'] ?: 'bottom';
$flip_curved_edge = $panel_settings['curve_flip'] ?: false;
$point_title_color = $panel_settings['point_title'] ?: '';
$column_background_color = $panel_settings['point_background'] ?: '';

// Set up background classes
$background_classes = 'has-background';
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
    if ($flip_curved_edge) {
        $background_classes .= ' curve-flipped';
    }
}

?>

<section class="steps-ahead steps-ahead--listing">
    <div class="steps-ahead__inner<?php echo $background_classes ? ' ' . $background_classes : ''; ?>" data-aos="fade" style="background-color: <?php echo $background_color; ?>;">
        <div class="container steps-ahead__container">
            <div class="steps-ahead__content centre inner-container">
                <?php if($panel_title) : ?>
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo esc_html($panel_title); ?></h2>
                <?php endif; ?>
                <?php if($panel_copy) : ?>
                    <div class="steps-ahead__copy content-area copy-large">
                        <?php echo $panel_copy; ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if($panel_columns) : ?>
                <div class="row steps-ahead__row">
                    <?php foreach($panel_columns as $column) : ?>
                        <?php
                        // Handle field structure from Theme Settings:
                        // Theme Settings: title, image, points (with title, text)
                        $ctitle = $column['title'] ?: '';
                        $cimage = $column['image'] ?: null;
                        $cpoints = $column['points'] ?: [];
                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre"<?php if($column_background_color) : ?> style="background-color: <?php echo $column_background_color; ?>;"<?php endif; ?>>
                                <?php if($cimage) : ?>
                                    <div class="steps-ahead__col-image">
                                        <img src="<?php echo $cimage['url']; ?>" alt="<?php echo $cimage['alt'] ?: ''; ?>">
                                    </div>
                                <?php endif; ?>

                                <?php if($ctitle) : ?>
                                    <h4 class="steps-ahead__col-heading"><?php echo esc_html($ctitle); ?></h4>
                                <?php endif; ?>

                                <?php if($cpoints && !empty($cpoints)) : ?>
                                    <div class="steps-ahead__col-points">
                                        <?php foreach($cpoints as $point) : ?>
                                            <?php
                                            $point_title = $point['title'] ?: '';
                                            $point_text = $point['text'] ?: '';
                                            ?>
                                            <?php if($point_title || $point_text) : ?>
                                                <div class="steps-ahead__point">
                                                    <?php if($point_title) : ?>
                                                        <h5 class="steps-ahead__point-title"<?php if($point_title_color) : ?> style="color: <?php echo $point_title_color; ?>;"<?php endif; ?>><?php echo esc_html($point_title); ?></h5>
                                                    <?php endif; ?>
                                                    <?php if($point_text) : ?>
                                                        <div class="steps-ahead__point-text">
                                                            <?php echo nl2br(esc_html($point_text)); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->
