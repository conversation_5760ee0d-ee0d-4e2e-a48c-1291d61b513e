{"key": "group_all_holidays_featured", "title": "All Holidays Archive - Featured Holidays Panel", "fields": [{"key": "field_675c4000f3000", "label": "Featured Holidays Panel", "name": "featured_holidays_panel", "aria-label": "", "type": "group", "instructions": "Panel that appears between the top CTA and holiday listings on the All Holidays archive page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c4001f3001", "label": "Enable Panel", "name": "enable_panel", "aria-label": "", "type": "true_false", "instructions": "Show the featured holidays panel on the All Holidays archive page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Show featured holidays panel", "default_value": 0, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_675c4002f3002", "label": "Panel Title", "name": "panel_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c4001f3001", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Our most popular holidays", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c4003f3003", "label": "Featured Holidays", "name": "featured_holidays", "aria-label": "", "type": "repeater", "instructions": "Select up to 4 holidays to feature", "required": 0, "conditional_logic": [[{"field": "field_675c4001f3001", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "min": 1, "max": 4, "layout": "table", "button_label": "Add Holiday", "collapsed": "", "rows_per_page": 20, "sub_fields": [{"key": "field_675c4004f3004", "label": "Holiday", "name": "holiday", "aria-label": "", "type": "post_object", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["holiday"], "taxonomy": "", "return_format": "object", "multiple": 0, "allow_null": 0, "ui": 1, "bidirectional_target": [], "parent_repeater": "field_675c4003f3003"}]}]}, {"key": "field_675c6000f5001", "label": "Steps Ahead Panel", "name": "steps_ahead_panel", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c6001f5002", "label": "Enable Panel", "name": "enable_panel", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c6002f5003", "label": "Panel Title", "name": "panel_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_675c6003f5004", "label": "Panel Copy", "name": "panel_copy", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_675c6004f5005", "label": "Background Color", "name": "background_color", "aria-label": "", "type": "color_picker", "instructions": "Choose a background color for the panel", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "#eaf2f1", "enable_opacity": 1, "return_format": "string"}, {"key": "field_675c6005f5006", "label": "Curved Edge", "name": "curved_edge", "aria-label": "", "type": "select", "instructions": "Add a curved edge using the banner mask", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"none": "None", "top": "Top", "bottom": "Bottom"}, "default_value": "bottom", "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_675c6006f5007", "label": "Columns", "name": "columns", "aria-label": "", "type": "repeater", "instructions": "Add up to 4 columns for the steps ahead panel", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 4, "layout": "block", "button_label": "Add Column", "rows_per_page": 20, "sub_fields": [{"key": "field_675c6007f5008", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "parent_repeater": "field_675c6006f5007"}, {"key": "field_675c6008f5009", "label": "Heading", "name": "heading", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_675c6006f5007"}, {"key": "field_675c6009f5010", "label": "Copy", "name": "copy", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0, "parent_repeater": "field_675c6006f5007"}]}]}], "location": [[{"param": "options_page", "operator": "==", "value": "theme-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": false, "description": "", "show_in_rest": 0, "modified": 1750429156}