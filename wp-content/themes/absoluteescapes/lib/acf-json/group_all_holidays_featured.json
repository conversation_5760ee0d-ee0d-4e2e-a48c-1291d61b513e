{"key": "group_all_holidays_featured", "title": "All Holidays Archive - Featured Holidays Panel", "fields": [{"key": "field_675c4000f3000", "label": "Featured Holidays Panel", "name": "featured_holidays_panel", "aria-label": "", "type": "group", "instructions": "Panel that appears between the top CTA and holiday listings on the All Holidays archive page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c4001f3001", "label": "Panel Status", "name": "panel_status", "aria-label": "", "type": "true_false", "instructions": "Show the featured holidays panel on the All Holidays archive page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Show featured holidays panel", "default_value": 0, "ui_on_text": "Yes", "ui_off_text": "No", "ui": 1}, {"key": "field_675c4002f3002", "label": "Panel Title", "name": "panel_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c4001f3001", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Our most popular holidays", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c4003f3003", "label": "Featured Holidays", "name": "featured_holidays", "aria-label": "", "type": "repeater", "instructions": "Select up to 4 holidays to feature", "required": 0, "conditional_logic": [[{"field": "field_675c4001f3001", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "min": 1, "max": 4, "layout": "table", "button_label": "Add Holiday", "collapsed": "", "rows_per_page": 20, "sub_fields": [{"key": "field_675c4004f3004", "label": "Holiday", "name": "holiday", "aria-label": "", "type": "post_object", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["holiday"], "taxonomy": "", "return_format": "object", "multiple": 0, "allow_null": 0, "ui": 1, "bidirectional_target": [], "parent_repeater": "field_675c4003f3003"}]}]}, {"key": "field_675c6000f5001", "label": "Steps Ahead Panel", "name": "steps_ahead_panel", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c6001f5002", "label": "Enable Panel", "name": "enable_panel", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c6004f5005", "label": "Panel Background", "name": "panel_background", "aria-label": "", "type": "color_picker", "instructions": "Choose a background color for the panel", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "enable_opacity": 1, "return_format": "string"}, {"key": "field_675c6005f5006", "label": "Curve Position", "name": "curve_position", "aria-label": "", "type": "select", "instructions": "Add a curved edge using the banner mask", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"none": "None", "top": "Top", "bottom": "Bottom"}, "default_value": "bottom", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_68557f124e6a8", "label": "<PERSON><PERSON>ve <PERSON>", "name": "curve_flip", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_68557f274e6a9", "label": "Point Title", "name": "point_title", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "#75ada4", "enable_opacity": 0, "return_format": "string"}, {"key": "field_68557f394e6aa", "label": "Point Background", "name": "point_background", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "#eaf2f1", "enable_opacity": 0, "return_format": "string"}]}], "location": [[{"param": "post_type", "operator": "==", "value": "holiday"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1750433640}