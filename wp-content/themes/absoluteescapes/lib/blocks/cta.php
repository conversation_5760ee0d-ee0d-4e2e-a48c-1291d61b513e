<?php

/**
 * Call To Action Block
 */

$defaults = [
    'status' => true,
    'title' => 'Talk to our travel experts',
    'text' => 'Our specialists combine first-hand experience with meticulous planning to craft holidays unique to you. Enquire today to plan your perfect escape.',
    'button_label' => 'Make an Enquiry',
    'button_link' => '/general-enquiry/',
    'icon' => '<svg class="svg-inline--fa fa-phone fa-w-16" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="phone" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"></path></svg>',
    'contact_number' => '+44 (0) ************',
    'use_image' => true,
];

// Get the CTA data from ACF fields
$cta_status = get_sub_field('status');
$cta_title = get_sub_field('title');
$cta_text = get_sub_field('text');
$cta_button_label = get_sub_field('button_label');
$cta_button_link = get_sub_field('button_link');
$cta_icon = get_sub_field('icon');
$cta_contact_number = get_sub_field('contact_number');
$cta_use_image = get_sub_field('use_image');
$cta_image = get_sub_field('image');
$cta_background_color = get_sub_field('background_color');
$cta_curved_edge = get_sub_field('curved_edge');
$cta_flip_curved_edge = get_sub_field('flip_curved_edge');

// Build the CTA array with fallbacks to defaults
$cta = [
    'status' => $cta_status !== null ? $cta_status : $defaults['status'],
    'title' => !empty($cta_title) ? $cta_title : $defaults['title'],
    'text' => !empty($cta_text) ? $cta_text : $defaults['text'],
    'button_label' => !empty($cta_button_label) ? $cta_button_label : $defaults['button_label'],
    'button_link' => !empty($cta_button_link) ? $cta_button_link : $defaults['button_link'],
    'icon' => !empty($cta_icon) ? $cta_icon : $defaults['icon'],
    'contact_number' => !empty($cta_contact_number) ? $cta_contact_number : $defaults['contact_number'],
    'use_image' => $cta_use_image !== null ? $cta_use_image : $defaults['use_image'],
    'image' => $cta_image,
    'background_color' => !empty($cta_background_color) ? $cta_background_color : '',
    'curved_edge' => !empty($cta_curved_edge) ? $cta_curved_edge : 'none',
    'flip_curved_edge' => $cta_flip_curved_edge ?: false,
];

if ($cta['status'] === true) {
    // Build CSS classes and styles
    $cta_classes = ['listing-cta', 'cta-block'];

    // Add curve classes to the background div
    $background_classes = ['listing-cta__background'];
    if ($cta['curved_edge'] === 'top') {
        $background_classes[] = 'curve-top';
        if ($cta['flip_curved_edge']) {
            $background_classes[] = 'curve-flipped';
        }
    } elseif ($cta['curved_edge'] === 'bottom') {
        $background_classes[] = 'curve-bottom';
        if ($cta['flip_curved_edge']) {
            $background_classes[] = 'curve-flipped';
        }
    }

    $background_style = '';
    if (!empty($cta['background_color'])) {
        $background_style = ' style="background-color: ' . esc_attr($cta['background_color']) . ';"';
    }
?>
<!-- Call To Action -->
<div class="<?php echo implode(' ', $cta_classes); ?>">
    <div class="<?php echo implode(' ', $background_classes); ?>"<?php echo $background_style; ?>>
        <div class="container">
            <div class="listing-cta__content">
               <?php if ($cta['use_image'] === true && !empty($cta['image'])) { ?>
                <div class="listing-cta__image">
                           <img loading="lazy" src="<?php echo $cta['image']['url'] ?>" alt="<?php echo $cta['image']['alt'] ?>">
                </div>
                <?php } ?>
                <div class="listing-cta__text<?php if ($cta['use_image'] !== true || empty($cta['image'])) { echo ' centre'; } ?>">
                    <?php if (!empty($cta['title'])) { ?>
                        <h2 class="text-block__heading heading-light text-weight-regular"><?php echo $cta['title']; ?></h2>
                    <?php } ?>
                    <?php if (!empty($cta['text'])) { ?>
                        <p><?php echo $cta['text']; ?></p>
                    <?php } ?>
                    <?php if ((!empty($cta['button_link']) && !empty($cta['button_label'])) || !empty($cta['icon']) || !empty($cta['contact_number'])) { ?>
                        <p class="listing-cta__actions">
                    <?php } ?>
                    <?php if (!empty($cta['button_link']) && !empty($cta['button_label'])) { ?>
                     <a href="<?php echo $cta['button_link']; ?>" class="button"><?php echo $cta['button_label']; ?></a>
                     <?php } ?>
                     <?php if (!empty($cta['contact_number'])) { ?>
                      <a href="tel:<?php echo preg_replace('/[^0-9\+]/', '', $cta['contact_number']); ?>">
                        <?php if (!empty($cta['icon'])) { ?>
                            <?php echo $cta['icon']; ?>
                        <?php } ?>
                        <strong><?php echo $cta['contact_number']; ?></strong>
                       </a>
                     <?php } ?>
                     <?php if ((!empty($cta['button_link']) && !empty($cta['button_label'])) || !empty($cta['icon']) || !empty($cta['contact_number'])) { ?>
                     </p>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- / Call To Action -->
<?php } ?>
