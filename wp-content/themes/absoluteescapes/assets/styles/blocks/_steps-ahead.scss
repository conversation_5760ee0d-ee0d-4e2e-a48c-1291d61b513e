.steps-ahead {

    // Hero section styles
    &__hero {
        position: relative;
        overflow: hidden;
    }

    &__hero-image {
        position: relative;

        img {
            width: 100%;
            height: auto;
            display: block;
        }
    }

    &__hero-content {
        position: absolute;
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        z-index: 2;
        width: 100%;
        padding: 0 20px;
    }

    &__hero-title {
        color: white;
        font-size: 3.5rem;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        margin: 0;
        line-height: 1.2;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 2.5rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            font-size: 2rem;
        }
    }

    &__inner {
        padding: 50px 0;
        position: relative;

        // Background color support
        &.has-background {
            // Default padding for background
            padding: 60px 0;
        }

        // Curved edge support
        &.curve-top {
            padding: 100px 0 40px 0;

            &::before {
                content: '';
                display: block;
                position: absolute;
                top: -2px;
                left: 0;
                width: 100%;
                height: 60px;
                background-image: url('../img/banner-mask.svg');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: top center;
                transform: scaleY(-1);
                z-index: 1;
            }

            // Flipped version
            &.curve-flipped::before {
                transform: scaleY(-1) scaleX(-1);
            }
        }

        &.curve-bottom {
            padding: 40px 0 100px 0;

            &::after {
                content: '';
                display: block;
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 100%;
                height: 60px;
                background-image: url('../img/banner-mask.svg');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: bottom center;
                transform: none;
                z-index: 1;
            }

            // Flipped version
            &.curve-flipped::after {
                transform: scaleX(-1);
            }
        }

        // Combined background and curve adjustments
        &.has-background.curve-top {
            padding: 120px 0 60px 0;
        }

        &.has-background.curve-bottom {
            padding: 60px 0 120px 0;
        }
    }

    &__row {
        padding-top: 60px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            padding-top: 45px;
        }
    }

    &__col-content {
        padding: 0 35px;
        margin-bottom: 30px;
        text-align: center;

        // When column has background color, add internal padding
        &[style*="background-color"] {
            padding: 30px 35px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 0 15px;

            &[style*="background-color"] {
                padding: 25px 15px;
            }
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 0 25px;

            &[style*="background-color"] {
                padding: 25px;
            }
        }
    }

    &__col-image {
        margin-bottom: 20px;

        img {
            width: 100%;
            max-width: 120px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
    }

    &__col-heading {
        color: #7bb3b0; // Teal color as shown in the image
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 20px;
        line-height: 1.3;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 1.2rem;
        }
    }

    &__col-points {
        text-align: left;
    }

    &__point {
        margin-bottom: 15px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    &__point-title {
        color: #333;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 5px;
        line-height: 1.4;
    }

    &__point-text {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 0;
    }

    &__col-copy {
        color: #666;
        font-size: 1rem;
        line-height: 1.6;

        p {
            margin-bottom: 0;
        }
    }

    // Legacy support for old image-based columns
    &__image {
        padding-bottom: 20px;
    }

    &__heading {
        margin-bottom: 10px;
    }

    // Listing page specific styles
    &--listing {
        .steps-ahead__inner {
            // Default padding for listing pages
            padding: 80px 0 40px;

            &.curve-bottom {
                padding: 80px 0 100px 0;
            }
        }
    }
}