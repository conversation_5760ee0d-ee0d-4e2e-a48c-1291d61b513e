.favourite-holidays {

    a {
        text-decoration: none;

        &:hover, &:focus {
            text-decoration: none;
        }
    }
    
    &__inner {
        padding: 50px 0;
    }

    &__container {
        &--carousel {
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                width: 100%;
                max-width: none;
                padding: 0;
            }
        }
    }

    &__content {
        padding-bottom: 60px;


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding-bottom: 45px;
        }
    }

    &__col {
        width: 300px;
        padding: 0 2px;

        a {
            display: block;
            position: relative;

            &:after {
                content: '';
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba($black, 0);
                transition: 300ms;
                z-index: 3;
            }

            &:hover, &:focus {
                &:after {
                    background: rgba($black, 0.2);
                }
            }
        }
    }

    &__background {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-end;
        height: 445px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;

        // Add gradient overlay for text readability
        &::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            z-index: 1;
            pointer-events: none;
        }
    }

    &__col-content {
        position: relative;
        z-index: 2;
        padding: 20px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-start;
        box-sizing: border-box;
    }

    &__title {
        margin: 0;
    }

    &__price {
        font-weight: 300;
    }

    &__types {
        position: absolute;
        bottom: 20px;
        right: 20px;
        z-index: 3;
        display: flex;
        gap: 8px;
        margin-bottom: 0;
        flex-wrap: wrap;
        flex-direction: column;
        align-items: flex-end;
    }

    &__type {
        // Individual type styling
    }

    &__icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3e5056;

        img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1);
        }

        svg {
            width: 22px;
            height: 22px;
            fill: white;
            color: white;
        }

        i {
            font-size: 18px;
            color: white;
        }
    }

    .flickity-button {
        border-color: $blue;
        background: $blue;
        color: $white;
        box-shadow: 0 0 25px rgba($black, 0.35);

        &:hover, &:focus {
            border-color: $teal;
            background: $teal;
            color: $white;
        }

        &:disabled {
            opacity: 0;
        }
    }

    .flickity-page-dots {
        display: none;
        bottom: -50px;


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: block;
        }

        .dot {
            background: $bluegrey;
        }
    }
    
}