!function(d,s,l){var c,g,u,f,p,m,w,v,x,y,b,T,C,H,h,k,r,a,W,E,I,M,L,F,R,S,K,P,B,O,_,j,n,o={html:!1,photo:!1,iframe:!1,inline:!1,transition:"elastic",speed:300,fadeOut:300,width:!1,initialWidth:"600",innerWidth:!1,maxWidth:!1,height:!1,initialHeight:"450",innerHeight:!1,maxHeight:!1,scalePhotos:!0,scrolling:!0,opacity:.9,preloading:!0,className:!1,overlayClose:!0,escKey:!0,arrowKey:!0,top:!1,bottom:!1,left:!1,right:!1,fixed:!1,data:void 0,closeButton:!0,fastIframe:!0,open:!1,reposition:!0,loop:!0,slideshow:!1,slideshowAuto:!0,slideshowSpeed:2500,slideshowStart:"start slideshow",slideshowStop:"stop slideshow",photoRegex:/\.(gif|png|jp(e|g|eg)|bmp|ico|webp|jxr|svg)((#|\?).*)?$/i,retinaImage:!1,retinaUrl:!1,retinaSuffix:"@2x.$1",current:"image {current} of {total}",previous:"previous",next:"next",close:"close",xhrError:"This content failed to load.",imgError:"This image failed to load.",returnFocus:!0,trapFocus:!0,onOpen:!1,onLoad:!1,onComplete:!1,onCleanup:!1,onClosed:!1,rel:function(){return this.rel},href:function(){return d(this).attr("href")},title:function(){return this.title},createImg:function(){var i=new Image,t=d(this).data("cbox-img-attrs");return"object"==typeof t&&d.each(t,function(t,e){i[t]=e}),i},createIframe:function(){var i=s.createElement("iframe"),t=d(this).data("cbox-iframe-attrs");return"object"==typeof t&&d.each(t,function(t,e){i[t]=e}),"frameBorder"in i&&(i.frameBorder=0),"allowTransparency"in i&&(i.allowTransparency="true"),i.name=(new Date).getTime(),i.allowFullscreen=!0,i}},D="wflscolorbox",N="wflscbox",z=N+"Element",A=N+"_open",q=N+"_load",U=N+"_complete",$=N+"_cleanup",G=N+"_closed",Q=N+"_purge",J=d("<a/>"),V="div",X=0,Y={};function Z(t,e,i){var n=s.createElement(t);return e&&(n.id=N+e),i&&(n.style.cssText=i),d(n)}function tt(){return l.innerHeight?l.innerHeight:d(l).height()}function et(t,i){i!==Object(i)&&(i={}),this.cache={},this.el=t,this.value=function(t){var e;return void 0===this.cache[t]&&(void 0!==(e=d(this.el).attr("data-cbox-"+t))?this.cache[t]=e:void 0!==i[t]?this.cache[t]=i[t]:void 0!==o[t]&&(this.cache[t]=o[t])),this.cache[t]},this.get=function(t){var e=this.value(t);return d.isFunction(e)?e.call(this.el,this):e}}function it(t){var e=x.length,i=(S+t)%e;return i<0?e+i:i}function nt(t,e){return Math.round((/%/.test(t)?("x"===e?y.width():tt())/100:1)*parseInt(t,10))}function ot(t,e){return t.get("photo")||t.get("photoRegex").test(e)}function ht(t,e){return t.get("retinaUrl")&&1<l.devicePixelRatio?e.replace(t.get("photoRegex"),t.get("retinaSuffix")):e}function rt(t){"contains"in g[0]&&!g[0].contains(t.target)&&t.target!==c[0]&&(t.stopPropagation(),g.focus())}function at(t){at.str!==t&&(g.add(c).removeClass(at.str).addClass(t),at.str=t)}function st(t){d(s).trigger(t),J.triggerHandler(t)}var lt=function(){var t,e,i=N+"Slideshow_",n="click."+N;function o(){clearTimeout(e)}function h(){(I.get("loop")||x[S+1])&&(o(),e=setTimeout(j.next,I.get("slideshowSpeed")))}function r(){k.html(I.get("slideshowStop")).unbind(n).one(n,a),J.bind(U,h).bind(q,o),g.removeClass(i+"off").addClass(i+"on")}function a(){o(),J.unbind(U,h).unbind(q,o),k.html(I.get("slideshowStart")).unbind(n).one(n,function(){j.next(),r()}),g.removeClass(i+"on").addClass(i+"off")}function s(){t=!1,k.hide(),o(),J.unbind(U,h).unbind(q,o),g.removeClass(i+"off "+i+"on")}return function(){t?I.get("slideshow")||(J.unbind($,s),s()):I.get("slideshow")&&x[1]&&(t=!0,J.one($,s),I.get("slideshowAuto")?r():a(),k.show())}}();function dt(t){var e,i;if(!O){if(e=d(t).data(D),I=new et(t,e),i=I.get("rel"),S=0,i&&!1!==i&&"nofollow"!==i?(x=d("."+z).filter(function(){return new et(this,d.data(this,D)).get("rel")===i}),-1===(S=x.index(I.el))&&(x=x.add(I.el),S=x.length-1)):x=d(I.el),!P){P=B=!0,at(I.get("className")),g.css({visibility:"hidden",display:"block",opacity:""}),b=Z(V,"LoadedContent","width:0; height:0; overflow:hidden; visibility:hidden"),f.css({width:"",height:""}).append(b),M=p.height()+v.height()+f.outerHeight(!0)-f.height(),L=m.width()+w.width()+f.outerWidth(!0)-f.width(),F=b.outerHeight(!0),R=b.outerWidth(!0);var n=nt(I.get("initialWidth"),"x"),o=nt(I.get("initialHeight"),"y"),h=I.get("maxWidth"),r=I.get("maxHeight");I.w=Math.max((!1!==h?Math.min(n,nt(h,"x")):n)-R-L,0),I.h=Math.max((!1!==r?Math.min(o,nt(r,"y")):o)-F-M,0),b.css({width:"",height:I.h}),j.position(),st(A),I.get("onOpen"),E.add(H).hide(),g.focus(),I.get("trapFocus")&&s.addEventListener&&(s.addEventListener("focus",rt,!0),J.one(G,function(){s.removeEventListener("focus",rt,!0)})),I.get("returnFocus")&&J.one(G,function(){d(I.el).focus()})}var a=parseFloat(I.get("opacity"));c.css({opacity:a==a?a:"",cursor:I.get("overlayClose")?"pointer":"",visibility:"visible"}).show(),I.get("closeButton")?W.html(I.get("close")).appendTo(f):W.appendTo("<div/>"),function(){var t,e,i,n=j.prep,o=++X;K=!(B=!0),st(Q),st(q),I.get("onLoad"),I.h=I.get("height")?nt(I.get("height"),"y")-F-M:I.get("innerHeight")&&nt(I.get("innerHeight"),"y"),I.w=I.get("width")?nt(I.get("width"),"x")-R-L:I.get("innerWidth")&&nt(I.get("innerWidth"),"x"),I.mw=I.w,I.mh=I.h,I.get("maxWidth")&&(I.mw=nt(I.get("maxWidth"),"x")-R-L,I.mw=I.w&&I.w<I.mw?I.w:I.mw);I.get("maxHeight")&&(I.mh=nt(I.get("maxHeight"),"y")-F-M,I.mh=I.h&&I.h<I.mh?I.h:I.mh);if(t=I.get("href"),_=setTimeout(function(){C.show()},100),I.get("inline")){var h=d(t).eq(0);i=d("<div>").hide().insertBefore(h),J.one(Q,function(){i.replaceWith(h)}),n(h)}else I.get("iframe")?n(" "):I.get("html")?n(I.get("html")):ot(I,t)?(t=ht(I,t),K=I.get("createImg"),d(K).addClass(N+"Photo").bind("error."+N,function(){n(Z(V,"Error").html(I.get("imgError")))}).one("load",function(){o===X&&setTimeout(function(){var t;I.get("retinaImage")&&1<l.devicePixelRatio&&(K.height=K.height/l.devicePixelRatio,K.width=K.width/l.devicePixelRatio),I.get("scalePhotos")&&(e=function(){K.height-=K.height*t,K.width-=K.width*t},I.mw&&K.width>I.mw&&(t=(K.width-I.mw)/K.width,e()),I.mh&&K.height>I.mh&&(t=(K.height-I.mh)/K.height,e())),I.h&&(K.style.marginTop=Math.max(I.mh-K.height,0)/2+"px"),x[1]&&(I.get("loop")||x[S+1])&&(K.style.cursor="pointer",d(K).bind("click."+N,function(){j.next()})),K.style.width=K.width+"px",K.style.height=K.height+"px",n(K)},1)}),K.src=t):t&&T.load(t,I.get("data"),function(t,e){o===X&&n("error"===e?Z(V,"Error").html(I.get("xhrError")):d(this).contents())})}()}}function ct(){g||(n=!1,y=d(l),g=Z(V).attr({id:D,class:!1===d.support.opacity?N+"IE":"",role:"dialog",tabindex:"-1"}).hide(),c=Z(V,"Overlay").hide(),C=d([Z(V,"LoadingOverlay")[0],Z(V,"LoadingGraphic")[0]]),u=Z(V,"Wrapper"),f=Z(V,"Content").append(H=Z(V,"Title"),h=Z(V,"Current"),a=d('<button type="button"/>').attr({id:N+"Previous"}),r=d('<button type="button"/>').attr({id:N+"Next"}),k=d('<button type="button"/>').attr({id:N+"Slideshow"}),C),W=d('<button type="button"/>').attr({id:N+"Close"}),u.append(Z(V).append(Z(V,"TopLeft"),p=Z(V,"TopCenter"),Z(V,"TopRight")),Z(V,!1,"clear:left").append(m=Z(V,"MiddleLeft"),f,w=Z(V,"MiddleRight")),Z(V,!1,"clear:left").append(Z(V,"BottomLeft"),v=Z(V,"BottomCenter"),Z(V,"BottomRight"))).find("div div").css({float:"left"}),T=Z(V,!1,"position:absolute; width:9999px; visibility:hidden; display:none; max-width:none;"),E=r.add(a).add(h).add(k)),s.body&&!g.parent().length&&d(s.body).append(c,g.append(u,T))}d[D]||(d(ct),(j=d.fn[D]=d[D]=function(e,t){var i=this;return e=e||{},d.isFunction(i)&&(i=d("<a/>"),e.open=!0),i[0]&&(ct(),function(){function t(t){1<t.which||t.shiftKey||t.altKey||t.metaKey||t.ctrlKey||(t.preventDefault(),dt(this))}return!!g&&(n||(n=!0,r.click(function(){j.next()}),a.click(function(){j.prev()}),W.click(function(){j.close()}),c.click(function(){I.get("overlayClose")&&j.close()}),d(s).bind("keydown."+N,function(t){var e=t.keyCode;P&&I.get("escKey")&&27===e&&(t.preventDefault(),j.close()),P&&I.get("arrowKey")&&x[1]&&!t.altKey&&(37===e?(t.preventDefault(),a.click()):39===e&&(t.preventDefault(),r.click()))}),d.isFunction(d.fn.on)?d(s).on("click."+N,"."+z,t):d("."+z).live("click."+N,t)),!0)}()&&(t&&(e.onComplete=t),i.each(function(){var t=d.data(this,D)||{};d.data(this,D,d.extend(t,e))}).addClass(z),new et(i[0],e).get("open")&&dt(i[0]))),i}).position=function(e,t){var i,n,o,h=0,r=0,a=g.offset();function s(){p[0].style.width=v[0].style.width=f[0].style.width=parseInt(g[0].style.width,10)-L+"px",f[0].style.height=m[0].style.height=w[0].style.height=parseInt(g[0].style.height,10)-M+"px"}if(y.unbind("resize."+N),g.css({top:-9e4,left:-9e4}),n=y.scrollTop(),o=y.scrollLeft(),I.get("fixed")?(a.top-=n,a.left-=o,g.css({position:"fixed"})):(h=n,r=o,g.css({position:"absolute"})),!1!==I.get("right")?r+=Math.max(y.width()-I.w-R-L-nt(I.get("right"),"x"),0):!1!==I.get("left")?r+=nt(I.get("left"),"x"):r+=Math.round(Math.max(y.width()-I.w-R-L,0)/2),!1!==I.get("bottom")?h+=Math.max(tt()-I.h-F-M-nt(I.get("bottom"),"y"),0):!1!==I.get("top")?h+=nt(I.get("top"),"y"):h+=Math.round(Math.max(tt()-I.h-F-M,0)/2),g.css({top:a.top,left:a.left,visibility:"visible"}),u[0].style.width=u[0].style.height="9999px",i={width:I.w+R+L,height:I.h+F+M,top:h,left:r},e){var l=0;d.each(i,function(t){i[t]===Y[t]||(l=e)}),e=l}Y=i,e||g.css(i),g.dequeue().animate(i,{duration:e||0,complete:function(){s(),B=!1,u[0].style.width=I.w+R+L+"px",u[0].style.height=I.h+F+M+"px",I.get("reposition")&&setTimeout(function(){y.bind("resize."+N,j.position)},1),d.isFunction(t)&&t()},step:s})},j.resize=function(t){var e;P&&((t=t||{}).width&&(I.w=nt(t.width,"x")-R-L),t.innerWidth&&(I.w=nt(t.innerWidth,"x")),b.css({width:I.w}),t.height&&(I.h=nt(t.height,"y")-F-M),t.innerHeight&&(I.h=nt(t.innerHeight,"y")),t.innerHeight||t.height||(e=b.scrollTop(),b.css({height:"auto"}),I.h=b.height()),b.css({height:I.h}),e&&b.scrollTop(e),j.position("none"===I.get("transition")?0:I.get("speed")))},j.prep=function(t){if(P){var e,o="none"===I.get("transition")?0:I.get("speed");b.remove(),(b=Z(V,"LoadedContent").append(t)).hide().appendTo(T.show()).css({width:(I.w=I.w||b.width(),I.w=I.mw&&I.mw<I.w?I.mw:I.w,I.w),overflow:I.get("scrolling")?"auto":"hidden"}).css({height:(I.h=I.h||b.height(),I.h=I.mh&&I.mh<I.h?I.mh:I.h,I.h)}).prependTo(f),T.hide(),d(K).css({float:"none"}),at(I.get("className")),e=function(){var t,e,i=x.length;function n(){!1===d.support.opacity&&g[0].style.removeAttribute("filter")}P&&(e=function(){clearTimeout(_),C.hide(),st(U),I.get("onComplete")},H.html(I.get("title")).show(),b.show(),1<i?("string"==typeof I.get("current")&&h.html(I.get("current").replace("{current}",S+1).replace("{total}",i)).show(),r[I.get("loop")||S<i-1?"show":"hide"]().html(I.get("next")),a[I.get("loop")||S?"show":"hide"]().html(I.get("previous")),lt(),I.get("preloading")&&d.each([it(-1),it(1)],function(){var t=x[this],e=new et(t,d.data(t,D)),i=e.get("href");i&&ot(e,i)&&(i=ht(e,i),s.createElement("img").src=i)})):E.hide(),I.get("iframe")?(t=I.get("createIframe"),I.get("scrolling")||(t.scrolling="no"),d(t).attr({src:I.get("href"),class:N+"Iframe"}).one("load",e).appendTo(b),J.one(Q,function(){t.src="//about:blank"}),I.get("fastIframe")&&d(t).trigger("load")):e(),"fade"===I.get("transition")?g.fadeTo(o,1,n):n())},"fade"===I.get("transition")?g.fadeTo(o,0,function(){j.position(0,e)}):j.position(o,e)}},j.next=function(){!B&&x[1]&&(I.get("loop")||x[S+1])&&(S=it(1),dt(x[S]))},j.prev=function(){!B&&x[1]&&(I.get("loop")||S)&&(S=it(-1),dt(x[S]))},j.close=function(){P&&!O&&(P=!(O=!0),st($),I.get("onCleanup"),y.unbind("."+N),c.fadeTo(I.get("fadeOut")||0,0),g.stop().fadeTo(I.get("fadeOut")||0,0,function(){g.hide(),c.hide(),st(Q),b.remove(),setTimeout(function(){O=!1,st(G),I.get("onClosed")},1)}))},j.remove=function(){g&&(g.stop(),d[D].close(),g.stop(!1,!0).remove(),c.remove(),O=!1,g=null,d("."+z).removeData(D).removeClass(z),d(s).unbind("click."+N).unbind("keydown."+N))},j.element=function(){return d(I.el)},j.settings=o)}(jQuery,document,window);