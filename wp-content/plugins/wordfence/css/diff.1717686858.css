body {
	background: #fff;
	font-family: Arial;
	font-size: 12px;
}
.Differences {
	width: 100%;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
}

.Differences thead th {
	text-align: left;
	border-bottom: 1px solid #000;
	background: #aaa;
	color: #000;
	padding: 4px;
}
.Differences tbody th {
	text-align: right;
	background: #ccc;
	width: 4em;
	padding: 1px 2px;
	border-right: 1px solid #000;
	vertical-align: top;
	font-size: 13px;
}

.Differences td {
	padding: 1px 2px;
	font-family: Consolas, monospace;
	font-size: 13px;
}

.DifferencesSideBySide td.Left, .DifferencesSideBySide td.Right {
	word-wrap: break-word;
	word-break: break-all;
}

.DifferencesSideBySide .ChangeInsert td.Left {
	background: #dfd;
}

.DifferencesSideBySide .ChangeInsert td.Right {
	background: #cfc;
}

.DifferencesSideBySide .ChangeDelete td.Left {
	background: #f88;
}

.DifferencesSideBySide .ChangeDelete td.Right {
	background: #faa;
}

.DifferencesSideBySide .ChangeReplace .Left {
	background: #fe9;
}

.DifferencesSideBySide .ChangeReplace .Right {
	background: #fd8;
}

.Differences ins, .Differences del {
	text-decoration: none;
}

.DifferencesSideBySide .ChangeReplace ins, .DifferencesSideBySide .ChangeReplace del {
	background: #fc0;
}

.Differences .Skipped {
	background: #f7f7f7;
}

.DifferencesInline .ChangeReplace .Left,
.DifferencesInline .ChangeDelete .Left {
	background: #fdd;
}

.DifferencesInline .ChangeReplace .Right,
.DifferencesInline .ChangeInsert .Right {
	background: #dfd;
}

.DifferencesInline .ChangeReplace ins {
	background: #9e9;
}

.DifferencesInline .ChangeReplace del {
	background: #e99;
}

pre {
	width: 100%;
	overflow: auto;
}
h1 {
	background: url(../images/wordfence-logo.svg);
	background-position: 0 0;
	background-repeat: no-repeat;
	height: 64px;
	vertical-align: middle;
	padding: 10px 0 0 90px;
	margin: 20px 0 10px 0;
	font-size: 34px;
	color: #777;
}
table.summary {
	font-weight: bold;
}
.diffFooter {
	text-align: center;
	font-size: 16px;
	color: #999;
	font-family: Verdana;
	margin: 50px auto 50px auto;
}
.diffFooter a {
	color: #999;
}

